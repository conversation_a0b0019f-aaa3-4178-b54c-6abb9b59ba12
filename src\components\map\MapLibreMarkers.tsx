import React, { useEffect, useRef } from 'react';
import maplibregl from 'maplibre-gl';
import { Incident, Response, IncidentType, ActionType } from '@/types/incident';
import { incidentTypeColors } from '@/services/map/mapService';
import { isValidCoordinates, getSafeCoordinates } from '@/utils/mapUtils';
import SuperCluster from 'supercluster';
import { tacticalSymbols, responseSymbols } from '@/components/map/TacticalSymbols';

interface MapLibreMarkersProps {
  map: maplibregl.Map | null;
  incidents: Incident[];
  responses: Response[];
  mode: 'normal' | 'cluster' | 'heatmap';
  onSelectIncident: (id: string) => void;
}

// Note: Tactical symbols are now imported from TacticalSymbols.tsx to maintain consistency

// Create a mapping for response symbols to handle string keys
const responseSymbolMapping = {
  'ADO': responseSymbols[ActionType.ADO],
  'ASO': responseSymbols[ActionType.ASO],
  'IBO': responseSymbols[ActionType.IBO],
  'SEARCH_OPS': responseSymbols[ActionType.SEARCH_OPS],
  'SEARCH_AND_CLEARANCE': responseSymbols[ActionType.SEARCH_AND_CLEARANCE],
  'COMPOUND_SEARCH': responseSymbols[ActionType.COMPOUND_SEARCH],
  'NONE': responseSymbols[ActionType.NONE]
};

const MapLibreMarkers: React.FC<MapLibreMarkersProps> = ({
  map,
  incidents,
  responses,
  mode,
  onSelectIncident
}) => {
  const markersRef = useRef<maplibregl.Marker[]>([]);
  const clusterRef = useRef<SuperCluster | null>(null);

  // Import the getSvgPathForSymbol function from TacticalSymbols.tsx
  // This ensures consistency with the Leaflet implementation
  const getSvgPathForSymbol = (symbolType: string): string => {
    // Use the same function from TacticalSymbols.tsx
    switch (symbolType) {
      // Original incident symbols - Enhanced NATO-style tactical symbols
      case 'physical_raid':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,12 L24,12 M8,20 L24,20" stroke="#fff" stroke-width="2.5" /><circle cx="16" cy="16" r="3" fill="#fff" />';

      case 'fire_raid':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,6 Q12,10 14,14 Q10,18 16,22 Q22,18 18,14 Q20,10 16,6" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

      case 'ambush':
        return '<polygon points="16,2 2,16 16,30 30,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,16 L24,16 M16,8 L16,24" stroke="#fff" stroke-width="2.5" /><circle cx="12" cy="12" r="2" fill="#fff" /><circle cx="20" cy="20" r="2" fill="#fff" />';

      case 'sniping':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16 M16,6 L16,26" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="6" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="2" fill="#fff" />';

      case 'post_overrun':
        return '<rect x="3" y="3" width="26" height="26" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M7,7 L25,25 M7,25 L25,7" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="3" fill="#fff" />';

      case 'post_fire':
        return '<rect x="3" y="3" width="26" height="26" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,7 Q12,11 14,15 Q10,19 16,23 Q22,19 18,15 Q20,11 16,7" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

      case 'demonstration':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="11" r="2.5" fill="#fff" /><path d="M8,20 Q16,25 24,20" stroke="#fff" stroke-width="2" fill="none" />';

      case 'target_killing':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,8 L24,24 M8,24 L24,8" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="5" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="1.5" fill="#fff" />';

      case 'arson':
        return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,6 Q12,10 14,14 Q10,18 16,22 Q22,18 18,14 Q20,10 16,6" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

      // TS (Tactical Situation) symbols - Enhanced with better visibility
      case 'ts_activity':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="3" fill="#fff" /><path d="M16,8 L16,24 M8,16 L24,16" stroke="#fff" stroke-width="1.5" />';

      case 'ts_infil':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16" stroke="#fff" stroke-width="3" /><path d="M20,10 L26,16 L20,22" stroke="#fff" stroke-width="2.5" fill="none" /><circle cx="8" cy="16" r="2" fill="#fff" />';

      case 'ts_presence':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.4" /><circle cx="16" cy="16" r="3" fill="#fff" />';

      case 'ts_mov':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16" stroke="#fff" stroke-width="3" /><path d="M18,10 L26,16 L18,22" stroke="#fff" stroke-width="2.5" fill="none" /><path d="M10,10 L18,16 L10,22" stroke="#fff" stroke-width="2" fill="none" />';

      case 'ts_taskeel':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="21" r="2.5" fill="#fff" /><path d="M10,16 L22,16" stroke="#fff" stroke-width="1.5" />';

      case 'ts_sb':
        return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><rect x="8" y="10" width="16" height="12" fill="#fff" fill-opacity="0.3" stroke="#fff" stroke-width="1" /><text x="16" y="18" font-size="8" text-anchor="middle" fill="#fff" font-weight="bold">SB</text>';

      case 'ts_extortion':
        return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="none" /><text x="16" y="18" font-size="7" text-anchor="middle" fill="#fff" font-weight="bold">EXT</text>';

      case 'ts_suspect':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="12" r="4" fill="#fff" /><path d="M12,20 Q16,24 20,20" stroke="#fff" stroke-width="2" fill="none" /><text x="16" y="26" font-size="6" text-anchor="middle" fill="#fff" font-weight="bold">SUSP</text>';

      case 'ts_meeting':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="11" r="2.5" fill="#fff" /><ellipse cx="16" cy="20" rx="8" ry="3" fill="#fff" fill-opacity="0.5" stroke="#fff" stroke-width="1" />';

      case 'ts_seen':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 Q16,6 26,16 Q16,26 6,16 Z" fill="#fff" fill-opacity="0.5" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="4" fill="#000" /><circle cx="16" cy="16" r="2" fill="#fff" />';

      case 'ts_tgt_killing':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,8 L24,24 M8,24 L24,8" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="6" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="2" fill="#fff" />';

      case 'ts_jirga':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="10" r="2" fill="#fff" /><circle cx="16" cy="10" r="2" fill="#fff" /><circle cx="22" cy="10" r="2" fill="#fff" /><ellipse cx="16" cy="20" rx="10" ry="4" fill="#fff" fill-opacity="0.3" stroke="#fff" stroke-width="1.5" /><path d="M8,22 Q16,18 24,22" stroke="#fff" stroke-width="1.5" fill="none" />';

      // Response symbols (all blue)
      case 'ado':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">ADO</text>';

      case 'aso':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">ASO</text>';

      case 'ibo':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">IBO</text>';

      case 'search_ops':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" />';

      case 'search_clearance':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" /><path d="M16,22 L16,26" stroke="#fff" stroke-width="1.5" />';

      case 'compound_search':
        return '<rect x="4" y="4" width="24" height="24" fill-opacity="0.8" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" />';

      // Response symbols (all blue)
      case 'ado':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">ADO</text>';

      case 'aso':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">ASO</text>';

      case 'ibo':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">IBO</text>';

      case 'search_ops':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" />';

      case 'search_clearance':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" /><path d="M16,22 L16,26" stroke="#fff" stroke-width="1.5" />';

      case 'compound_search':
        return '<rect x="4" y="4" width="24" height="24" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" />';

      case 'none':
        return '<rect x="2" y="2" width="28" height="28" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">NONE</text>';

      case 'other':
      default:
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><text x="16" y="20" font-size="14" text-anchor="middle" fill="#fff">?</text>';
    }
  };

  // Create tactical symbol SVG for incidents
  const createTacticalSymbolSvg = (type: IncidentType, size: number = 32) => {
    const symbol = tacticalSymbols[type] || tacticalSymbols[IncidentType.OTHER];
    const svgPath = getSvgPathForSymbol(symbol.symbol);

    // Debug logging (only for first few symbols)
    if (Math.random() < 0.1) {
      console.log('Creating symbol for type:', type, 'symbol:', symbol, 'svgPath length:', svgPath.length);
    }

    return `
      <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
        <g fill="${symbol.color}" stroke="#000" stroke-width="1">
          ${svgPath}
        </g>
      </svg>
    `;
  };

  // Response symbol mapping for MapLibre
  const responseSymbolMapping: Record<string, { symbol: string; color: string }> = {
    'ADO': { symbol: 'ado', color: '#0074D9' },
    'ASO': { symbol: 'aso', color: '#0074D9' },
    'IBO': { symbol: 'ibo', color: '#0074D9' },
    'SEARCH_OPS': { symbol: 'search_ops', color: '#0074D9' },
    'SEARCH_AND_CLEARANCE': { symbol: 'search_clearance', color: '#0074D9' },
    'COMPOUND_SEARCH': { symbol: 'compound_search', color: '#0074D9' },
    'NONE': { symbol: 'none', color: '#0074D9' }
  };

  // Create response symbol SVG
  const createResponseSymbolSvg = (type: string, size: number = 32) => {
    const symbol = responseSymbolMapping[type] || responseSymbolMapping['NONE'];
    const svgPath = getSvgPathForSymbol(symbol.symbol);

    return `
      <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
        <g fill="${symbol.color}" stroke="#000" stroke-width="1">
          ${svgPath}
        </g>
      </svg>
    `;
  };

  // Clean up existing markers
  const cleanupMarkers = () => {
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current = [];
  };

  // Create incident marker
  const createIncidentMarker = (incident: Incident) => {
    if (!isValidCoordinates(incident.location)) return null;

    const safeLocation = getSafeCoordinates(incident.location);
    const color = incidentTypeColors[incident.type] || '#AAAAAA';

    // Create marker element with tactical symbol
    const el = document.createElement('div');
    el.className = 'incident-marker';

    // Create symbol SVG (NATO or custom)
    const symbolSvg = createTacticalSymbolSvg(incident.type, 18);
    el.innerHTML = symbolSvg;

    el.style.cssText = `
      width: 18px;
      height: 18px;
      cursor: pointer;
      transition: transform 0.2s;
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
    `;

    el.addEventListener('mouseenter', () => {
      el.style.transform = 'scale(1.2)';
    });

    el.addEventListener('mouseleave', () => {
      el.style.transform = 'scale(1)';
    });

    el.addEventListener('click', () => {
      onSelectIncident(incident.id);
    });

    // Create popup
    const popup = new maplibregl.Popup({
      offset: 25,
      closeButton: true,
      closeOnClick: false
    }).setHTML(`
      <div class="p-3 bg-military-panel text-military-white">
        <h3 class="font-bold text-sm mb-2">${incident.title}</h3>
        <div class="text-xs space-y-1">
          <div><strong>Type:</strong> ${incident.type.replace(/_/g, ' ')}</div>
          <div><strong>Severity:</strong> ${incident.severity}</div>
          <div><strong>Status:</strong> ${incident.status}</div>
          <div><strong>Location:</strong> ${incident.address}</div>
          <div><strong>Reported:</strong> ${new Date(incident.reportedAt).toLocaleString()}</div>
        </div>
        <button
          onclick="window.dispatchEvent(new CustomEvent('incident:select', {detail: '${incident.id}'}))"
          class="mt-2 w-full bg-military-blue text-white px-2 py-1 text-xs rounded hover:bg-military-navy"
        >
          View Details
        </button>
      </div>
    `);

    const marker = new maplibregl.Marker(el)
      .setLngLat([safeLocation.longitude, safeLocation.latitude])
      .setPopup(popup);

    return marker;
  };

  // Create response marker
  const createResponseMarker = (response: Response) => {
    if (!isValidCoordinates(response.location)) return null;

    const safeLocation = getSafeCoordinates(response.location);

    // Create marker element with response symbol
    const el = document.createElement('div');
    el.className = 'response-marker';

    // Create symbol SVG (NATO or custom)
    const symbolSvg = createResponseSymbolSvg(response.type, 14);
    el.innerHTML = symbolSvg;

    el.style.cssText = `
      width: 14px;
      height: 14px;
      cursor: pointer;
      transition: transform 0.2s;
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
    `;

    el.addEventListener('mouseenter', () => {
      el.style.transform = 'scale(1.2)';
    });

    el.addEventListener('mouseleave', () => {
      el.style.transform = 'scale(1)';
    });

    // Create popup
    const popup = new maplibregl.Popup({
      offset: 25,
      closeButton: true,
      closeOnClick: false
    }).setHTML(`
      <div class="p-3 bg-military-panel text-military-white">
        <h3 class="font-bold text-sm mb-2">${response.title}</h3>
        <div class="text-xs space-y-1">
          <div><strong>Type:</strong> ${response.type.replace(/_/g, ' ')}</div>
          <div><strong>Status:</strong> ${response.status}</div>
          <div><strong>Commander:</strong> ${response.commander}</div>
          <div><strong>Location:</strong> ${response.address}</div>
          <div><strong>Start:</strong> ${new Date(response.startDate).toLocaleString()}</div>
        </div>
        <button
          onclick="window.dispatchEvent(new CustomEvent('response:select', {detail: '${response.id}'}))"
          class="mt-2 w-full bg-military-blue text-white px-2 py-1 text-xs rounded hover:bg-military-navy"
        >
          View Details
        </button>
      </div>
    `);

    const marker = new maplibregl.Marker(el)
      .setLngLat([safeLocation.longitude, safeLocation.latitude])
      .setPopup(popup);

    return marker;
  };

  // Create cluster markers
  const createClusterMarkers = () => {
    if (!map) return;

    // Prepare data for clustering
    const points = incidents
      .filter(incident => isValidCoordinates(incident.location))
      .map(incident => {
        const safeLocation = getSafeCoordinates(incident.location);
        return {
          type: 'Feature',
          properties: {
            cluster: false,
            incidentId: incident.id,
            incidentType: incident.type
          },
          geometry: {
            type: 'Point',
            coordinates: [safeLocation.longitude, safeLocation.latitude]
          }
        };
      });

    // Initialize cluster
    clusterRef.current = new SuperCluster({
      radius: 40,
      maxZoom: 16
    });

    clusterRef.current.load(points);

    // Get clusters for current zoom and bounds
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());

    const clusters = clusterRef.current.getClusters(
      [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()],
      zoom
    );

    clusters.forEach(cluster => {
      const [lng, lat] = cluster.geometry.coordinates;

      if (cluster.properties.cluster) {
        // Create military-styled cluster marker
        const el = document.createElement('div');
        el.className = 'cluster-marker';

        // Determine cluster size and color based on count
        const count = cluster.properties.point_count;
        let size = 30;
        let bgColor = 'rgba(255, 65, 54, 0.8)'; // Military red
        let borderColor = '#fff';

        if (count > 50) {
          size = 50;
          bgColor = 'rgba(255, 0, 0, 0.9)'; // Darker red for large clusters
        } else if (count > 20) {
          size = 40;
          bgColor = 'rgba(255, 65, 54, 0.85)';
        }

        el.style.cssText = `
          width: ${size}px;
          height: ${size}px;
          border-radius: 50%;
          background: linear-gradient(135deg, ${bgColor}, rgba(180, 13, 201, 0.6));
          border: 3px solid ${borderColor};
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: ${Math.min(14, Math.max(10, size / 3))}px;
          cursor: pointer;
          box-shadow: 0 4px 8px rgba(0,0,0,0.4), inset 0 1px 2px rgba(255,255,255,0.3);
          transition: transform 0.2s, box-shadow 0.2s;
          font-family: 'Courier New', monospace;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
        `;
        el.textContent = count.toString();

        // Add hover effects
        el.addEventListener('mouseenter', () => {
          el.style.transform = 'scale(1.1)';
          el.style.boxShadow = '0 6px 12px rgba(0,0,0,0.5), inset 0 1px 2px rgba(255,255,255,0.3)';
        });

        el.addEventListener('mouseleave', () => {
          el.style.transform = 'scale(1)';
          el.style.boxShadow = '0 4px 8px rgba(0,0,0,0.4), inset 0 1px 2px rgba(255,255,255,0.3)';
        });

        el.addEventListener('click', () => {
          const expansionZoom = clusterRef.current!.getClusterExpansionZoom(cluster.properties.cluster_id);
          map.flyTo({
            center: [lng, lat],
            zoom: expansionZoom
          });
        });

        const marker = new maplibregl.Marker(el)
          .setLngLat([lng, lat]);

        markersRef.current.push(marker);
        marker.addTo(map);
      } else {
        // Create individual marker
        const incident = incidents.find(i => i.id === cluster.properties.incidentId);
        if (incident) {
          const marker = createIncidentMarker(incident);
          if (marker) {
            markersRef.current.push(marker);
            marker.addTo(map);
          }
        }
      }
    });
  };

  // Create heatmap
  const createHeatmap = () => {
    if (!map) return;

    // Remove existing heatmap
    if (map.getLayer('heatmap')) {
      map.removeLayer('heatmap');
    }
    if (map.getSource('incidents-heat')) {
      map.removeSource('incidents-heat');
    }

    // Prepare heatmap data
    const heatmapData = {
      type: 'FeatureCollection',
      features: incidents
        .filter(incident => isValidCoordinates(incident.location))
        .map(incident => {
          const safeLocation = getSafeCoordinates(incident.location);
          let weight = 1;
          switch (incident.severity) {
            case 'LOW': weight = 1; break;
            case 'MEDIUM': weight = 2; break;
            case 'HIGH': weight = 3; break;
            case 'CRITICAL': weight = 4; break;
          }

          return {
            type: 'Feature',
            properties: { weight },
            geometry: {
              type: 'Point',
              coordinates: [safeLocation.longitude, safeLocation.latitude]
            }
          };
        })
    };

    // Add heatmap source and layer
    map.addSource('incidents-heat', {
      type: 'geojson',
      data: heatmapData
    });

    map.addLayer({
      id: 'heatmap',
      type: 'heatmap',
      source: 'incidents-heat',
      maxzoom: 15,
      paint: {
        'heatmap-weight': ['get', 'weight'],
        'heatmap-intensity': ['interpolate', ['linear'], ['zoom'], 0, 1, 15, 3],
        'heatmap-color': [
          'interpolate',
          ['linear'],
          ['heatmap-density'],
          0, 'rgba(0, 0, 255, 0)',
          0.2, 'rgb(0, 0, 255)',
          0.4, 'rgb(0, 255, 255)',
          0.6, 'rgb(0, 255, 0)',
          0.8, 'rgb(255, 255, 0)',
          1, 'rgb(255, 0, 0)'
        ],
        'heatmap-radius': ['interpolate', ['linear'], ['zoom'], 0, 2, 15, 20],
        'heatmap-opacity': ['interpolate', ['linear'], ['zoom'], 7, 1, 15, 0]
      }
    });
  };

  // Update markers based on mode
  useEffect(() => {
    if (!map) return;

    // Debug logging
    console.log('MapLibreMarkers: Updating markers', {
      mode,
      incidentCount: incidents.length,
      responseCount: responses.length,
      incidents: incidents.slice(0, 3).map(i => ({ id: i.id, type: i.type, title: i.title }))
    });

    cleanupMarkers();

    // Remove existing heatmap
    if (map.getLayer('heatmap')) {
      map.removeLayer('heatmap');
    }
    if (map.getSource('incidents-heat')) {
      map.removeSource('incidents-heat');
    }

    switch (mode) {
      case 'normal':
        // Add individual markers
        incidents.forEach(incident => {
          const marker = createIncidentMarker(incident);
          if (marker) {
            markersRef.current.push(marker);
            marker.addTo(map);
          }
        });

        responses.forEach(response => {
          const marker = createResponseMarker(response);
          if (marker) {
            markersRef.current.push(marker);
            marker.addTo(map);
          }
        });
        break;

      case 'cluster':
        createClusterMarkers();
        break;

      case 'heatmap':
        createHeatmap();
        break;
    }

    return cleanupMarkers;
  }, [map, incidents, responses, mode]);

  // Listen for selection events
  useEffect(() => {
    const handleIncidentSelect = (event: CustomEvent) => {
      onSelectIncident(event.detail);
    };

    const handleResponseSelect = (event: CustomEvent) => {
      // Handle response selection
      const responseEvent = new CustomEvent('response:select', { detail: event.detail });
      window.dispatchEvent(responseEvent);
    };

    window.addEventListener('incident:select', handleIncidentSelect as EventListener);
    window.addEventListener('response:select', handleResponseSelect as EventListener);

    return () => {
      window.removeEventListener('incident:select', handleIncidentSelect as EventListener);
      window.removeEventListener('response:select', handleResponseSelect as EventListener);
    };
  }, [onSelectIncident]);

  return null;
};

export default MapLibreMarkers;
