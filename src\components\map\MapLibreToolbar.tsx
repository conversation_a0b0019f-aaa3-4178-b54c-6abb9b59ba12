import React, { useState, useEffect } from 'react';
import maplibregl from 'maplibre-gl';
import Button from '@/components/ui/Button';
import {
  MapPin,
  Home,
  Trash2,
  Maximize2,
  Minimize2,
  Crosshair,
  Map,
  Grid,
  Mountain,
  Globe,
  Moon,
  Satellite,
  Layers,
  Target,
  Circle,
  Square,
  Pencil,
  Box
} from 'lucide-react';

interface MapLibreToolbarProps {
  map: maplibregl.Map | null;
  activeBaseLayer: string;
  onBaseLayerChange: (layer: string) => void;
  mapMode: 'normal' | 'cluster' | 'heatmap';
  onMapModeChange: (mode: 'normal' | 'cluster' | 'heatmap') => void;
  is3D: boolean;
  onToggle3D: () => void;
}

const MapLibreToolbar: React.FC<MapLibreToolbarProps> = ({
  map,
  activeBaseLayer,
  onBaseLayerChange,
  mapMode,
  onMapModeChange,
  is3D,
  onToggle3D
}) => {
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [windowSize, setWindowSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  // Track window size for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Track mouse coordinates
  useEffect(() => {
    if (!map) return;

    const handleMouseMove = (e: maplibregl.MapMouseEvent) => {
      setCoordinates({
        lat: e.lngLat.lat,
        lng: e.lngLat.lng
      });
    };

    map.on('mousemove', handleMouseMove);

    return () => {
      map.off('mousemove', handleMouseMove);
    };
  }, [map]);

  const handleBaseLayerChange = (layer: string) => {
    onBaseLayerChange(layer);
  };

  const handleFullscreen = () => {
    if (!map) return;

    const container = map.getContainer();
    if (!document.fullscreenElement) {
      container.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleHome = () => {
    if (!map) return;

    map.flyTo({
      center: [71.5249, 34.0151], // Pakistan coordinates
      zoom: is3D ? 8 : 6, // Better zoom for 3D terrain
      pitch: is3D ? 60 : 0,
      bearing: 0,
      duration: 2000
    });
  };

  const adjustTerrainExaggeration = (exaggeration: number) => {
    if (!map || !is3D) return;

    try {
      map.setTerrain({
        source: 'terrarium',
        exaggeration: exaggeration
      });
    } catch (error) {
      console.warn('Could not adjust terrain exaggeration:', error);
    }
  };



  const clearDrawnItems = () => {
    if (!map) return;

    // Remove all drawn layers
    const layers = map.getStyle().layers;
    if (layers) {
      layers.forEach(layer => {
        if (layer.id.startsWith('drawn-')) {
          map.removeLayer(layer.id);
        }
      });
    }

    // Remove all drawn sources
    const sources = map.getStyle().sources;
    if (sources) {
      Object.keys(sources).forEach(sourceId => {
        if (sourceId.startsWith('drawn-')) {
          map.removeSource(sourceId);
        }
      });
    }
  };

  // Determine layout based on window size
  const isSmallScreen = windowSize.width < 768;
  const isShortScreen = windowSize.height < 600;
  const shouldUseCompactLayout = isSmallScreen || isShortScreen;

  return (
    <>
      {/* Coordinates display */}
      {coordinates && !isSmallScreen && (
        <div className="absolute bottom-2 left-16 z-[1000] military-control-container rounded-md shadow-lg p-2">
          <div className="text-xs font-mono text-military-white">
            <div>LAT: {coordinates.lat.toFixed(6)}</div>
            <div>LNG: {coordinates.lng.toFixed(6)}</div>
          </div>
        </div>
      )}

      {/* Terrain controls - top center (3D mode only) */}
      {is3D && (
        <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-[1000] military-control-container rounded-md shadow-lg overflow-hidden">
          <div className="flex gap-1 p-1">
            <Button
              size="sm"
              variant="ghost"
              className="military-button text-button"
              title="Low Terrain (1x)"
              onClick={() => adjustTerrainExaggeration(1)}
            >
              1x
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button text-button"
              title="Normal Terrain (1.5x)"
              onClick={() => adjustTerrainExaggeration(1.5)}
            >
              1.5x
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button text-button"
              title="High Terrain (2x)"
              onClick={() => adjustTerrainExaggeration(2)}
            >
              2x
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button text-button"
              title="Extreme Terrain (3x)"
              onClick={() => adjustTerrainExaggeration(3)}
            >
              3x
            </Button>
          </div>
        </div>
      )}

      {/* Navigation section label */}
      <div className="absolute top-24 left-2 z-[1000]">
        <div className="military-control-container rounded-md shadow-lg overflow-hidden">
          <div className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider">
            NAVIGATION
          </div>
        </div>
      </div>

      {/* Left side controls - drawing tools positioned below military navigation controls */}
      <div className="absolute top-36 left-2 z-[1000] flex flex-col gap-2">
        {/* Drawing tools section with header */}
        <div className="military-control-container rounded-md shadow-lg overflow-hidden">
          <div className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider border-b border-military-border">
            DRAWING TOOLS
          </div>
          <div className="flex flex-col gap-1 p-1">
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Draw Point"
              onClick={() => {
                const event = new CustomEvent('maplibre:startDraw', { detail: { mode: 'draw_point' } });
                window.dispatchEvent(event);
              }}
            >
              <MapPin size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Draw Line"
              onClick={() => {
                const event = new CustomEvent('maplibre:startDraw', { detail: { mode: 'draw_line_string' } });
                window.dispatchEvent(event);
              }}
            >
              <Pencil size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Draw Polygon"
              onClick={() => {
                const event = new CustomEvent('maplibre:startDraw', { detail: { mode: 'draw_polygon' } });
                window.dispatchEvent(event);
              }}
            >
              <Square size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Draw Circle"
              onClick={() => {
                const event = new CustomEvent('maplibre:startDraw', { detail: { mode: 'draw_circle' } });
                window.dispatchEvent(event);
              }}
            >
              <Circle size={14} />
            </Button>
          </div>
        </div>
      </div>

      {/* Right side controls - more space available since navigation moved to left */}
      <div className={`absolute ${shouldUseCompactLayout ? 'top-2 right-2' : 'top-2 right-2'} z-[1000] flex ${shouldUseCompactLayout ? 'flex-row gap-1' : 'flex-col gap-2'}`}>
        {/* Base layer controls */}
        <div className="military-control-container rounded-md shadow-lg overflow-hidden">
          <div className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider border-b border-military-border">
            BASE LAYERS
          </div>
          <div className="flex flex-col gap-1 p-1">
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'satellite' ? 'active' : ''}`}
              title="Satellite"
              onClick={() => handleBaseLayerChange('satellite')}
            >
              <Satellite size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'terrain' ? 'active' : ''}`}
              title="Terrain"
              onClick={() => handleBaseLayerChange('terrain')}
            >
              <Mountain size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'osm' ? 'active' : ''}`}
              title="OpenStreetMap"
              onClick={() => handleBaseLayerChange('osm')}
            >
              <Globe size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'dark' ? 'active' : ''}`}
              title="Dark"
              onClick={() => handleBaseLayerChange('dark')}
            >
              <Moon size={14} />
            </Button>
          </div>
        </div>

        {/* View mode controls */}
        <div className="military-control-container rounded-md shadow-lg overflow-hidden">
          <div className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider border-b border-military-border">
            VIEW MODES
          </div>
          <div className="flex flex-col gap-1 p-1">
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'normal' ? 'active' : ''}`}
              title="Normal View"
              onClick={() => onMapModeChange('normal')}
            >
              <MapPin size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'cluster' ? 'active' : ''}`}
              title="Cluster View"
              onClick={() => onMapModeChange('cluster')}
            >
              <Target size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'heatmap' ? 'active' : ''}`}
              title="Heatmap View"
              onClick={() => onMapModeChange('heatmap')}
            >
              <Grid size={14} />
            </Button>
          </div>
        </div>
      </div>

      {/* Bottom controls - positioned to avoid overlap with legend and scale control */}
      <div className={`absolute ${shouldUseCompactLayout ? 'bottom-2 right-2' : 'bottom-4 right-4'} z-[1000] military-control-container rounded-md shadow-lg overflow-hidden`}>
        <div className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider border-b border-military-border">
          MAP CONTROLS
        </div>
        <div className={`flex ${shouldUseCompactLayout ? 'flex-col' : 'flex-row'} gap-1 p-1`}>
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Home View"
            onClick={handleHome}
          >
            <Home size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Clear All Drawings"
            onClick={clearDrawnItems}
          >
            <Trash2 size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className={`military-button ${is3D ? 'active' : ''}`}
            title={is3D ? 'Switch to 2D' : 'Switch to 3D'}
            onClick={onToggle3D}
          >
            <Box size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Fullscreen"
            onClick={handleFullscreen}
          >
            {isFullscreen ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
          </Button>
        </div>
      </div>
    </>
  );
};

export default MapLibreToolbar;
